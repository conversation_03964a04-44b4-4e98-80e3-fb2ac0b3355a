# Session Management Bug Fix

## Issue Description
Users were encountering a "User ID not found in session" error when trying to generate authentication codes after successfully logging in. The error occurred in the `/api/auth/generate-code` endpoint.

## Root Cause
The issue was caused by improper NextAuth.js configuration when using a credentials provider with JWT strategy. Specifically:

1. **PrismaAdapter Conflict**: The `PrismaAdapter` was being used with a credentials provider and JWT strategy, which is incompatible. The adapter is designed for database sessions, not JWT tokens.

2. **Session Configuration**: The session callbacks weren't properly configured to handle the user ID in JWT tokens.

3. **Missing Auth Options**: The `getServerSession()` function wasn't receiving the auth configuration, causing it to fail to decode the session properly.

## Solution Implemented

### 1. Created Shared Auth Configuration (`src/lib/auth-config.ts`)
- Moved NextAuth configuration to a shared file for consistency
- Removed `PrismaAdapter` when using credentials provider with JWT
- Enhanced JWT and session callbacks to properly handle user data

### 2. Updated NextAuth Route (`src/app/api/auth/[...nextauth]/route.ts`)
- Simplified to use the shared auth configuration
- Removed duplicate configuration code

### 3. Fixed API Route (`src/app/api/auth/generate-code/route.ts`)
- Added `authOptions` parameter to `getServerSession()`
- Improved session data extraction with proper TypeScript types

### 4. Added TypeScript Types (`src/types/next-auth.d.ts`)
- Extended NextAuth types to include user ID in session
- Ensured type safety for session and JWT objects

### 5. Updated Middleware (`middleware.ts`)
- Added proper pages configuration for sign-in redirection

## Key Changes

### Before (Problematic Configuration)
```typescript
// Using adapter with credentials provider (incompatible)
export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma), // ❌ Causes issues with JWT
  providers: [CredentialsProvider({...})],
  session: { strategy: 'jwt' },
  // ...
}

// API route without auth options
const session = await getServerSession() // ❌ Can't decode properly
```

### After (Fixed Configuration)
```typescript
// No adapter with credentials provider + JWT
export const authOptions: NextAuthOptions = {
  // adapter: PrismaAdapter(prisma), // ✅ Removed
  providers: [CredentialsProvider({...})],
  session: { strategy: 'jwt' },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id // ✅ Properly store user ID
        // ...
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string // ✅ Properly extract user ID
        // ...
      }
      return session
    },
  },
}

// API route with auth options
const session = await getServerSession(authOptions) // ✅ Properly configured
```

## Testing Results
After implementing the fix:
- ✅ User login works correctly
- ✅ Session contains proper user ID: `"id": "683ca05a015646a124ecb25b"`
- ✅ Code generation API returns 200 status
- ✅ 6-digit authentication codes are generated successfully
- ✅ Cross-domain authentication flow works end-to-end

## Best Practices Applied
1. **Separation of Concerns**: Moved auth configuration to dedicated file
2. **Type Safety**: Added proper TypeScript types for NextAuth
3. **Consistency**: Used shared configuration across all auth-related code
4. **Documentation**: Added clear comments explaining the configuration choices

## Files Modified
- `src/lib/auth-config.ts` (new)
- `src/types/next-auth.d.ts` (new)
- `src/app/api/auth/[...nextauth]/route.ts`
- `src/app/api/auth/generate-code/route.ts`
- `middleware.ts`

The authentication system now works correctly with proper session management and user ID persistence across the application.
