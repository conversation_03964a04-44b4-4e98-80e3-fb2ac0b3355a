# FlowDub Cross-Domain Authentication System

A professional cross-domain authentication system built with Next.js, TypeScript, Prisma ORM, and MongoDB. This system allows users to authenticate on a primary domain and use generated 6-digit codes to access secondary domains securely.

## 🚀 Features

- **Cross-Domain Authentication**: Secure authentication between multiple domains
- **6-Digit Code System**: Time-limited authentication codes (15-minute expiry)
- **Professional Security**: Password hashing, session management, input validation
- **Modern Tech Stack**: Next.js 15, TypeScript, Prisma, MongoDB, NextAuth.js
- **Responsive UI**: Clean, professional interface with Tailwind CSS
- **Real-time Code Expiry**: Live countdown timers for authentication codes

## 🛠️ Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **Authentication**: NextAuth.js with custom credentials provider
- **Database**: MongoDB with Prisma ORM
- **Styling**: Tailwind CSS
- **Validation**: Zod for schema validation
- **Security**: bcryptjs for password hashing
- **HTTP Client**: Axios for API requests

## 📋 Prerequisites

- Node.js 18+
- MongoDB database (Atlas or local)
- npm or yarn package manager

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd flowdub-login
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**

   The `.env.local` file is already configured with:
   ```env
   # Database
   DATABASE_URL="mongodb+srv://beats:<EMAIL>/flowdub-auth"

   # NextAuth.js
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-super-secret-key-change-this-in-production-make-it-very-long-and-random"

   # App Configuration
   PRIMARY_DOMAIN="http://localhost:3000"
   SECONDARY_DOMAIN="http://localhost:3001"

   # Code Configuration
   AUTH_CODE_EXPIRY_MINUTES=15
   ```

4. **Database Setup**
   ```bash
   # Generate Prisma client
   npx prisma generate

   # Push database schema to MongoDB
   npx prisma db push
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

## 🌐 Usage

### Primary Domain (localhost:3000)

1. **Registration**: Navigate to `/register` to create a new account
2. **Login**: Use `/login` to authenticate with email/password
3. **Dashboard**: After login, access `/dashboard` to:
   - View account information
   - Generate 6-digit authentication codes
   - Copy codes for cross-domain access

### Secondary Domain (localhost:3001 or /verify)

1. **Code Verification**: Navigate to `/verify`
2. **Enter Code**: Input the 6-digit code from the primary domain
3. **Access Granted**: Successful verification provides access to secondary domain features

## 🔐 Security Features

- **Password Hashing**: bcryptjs with salt rounds
- **Session Management**: Secure JWT-based sessions via NextAuth.js
- **Code Expiration**: 15-minute automatic expiry for authentication codes
- **One-time Use**: Codes are invalidated after single use
- **Input Validation**: Zod schemas for all user inputs
- **CORS Configuration**: Proper cross-domain request handling
- **Route Protection**: Middleware-based authentication guards

## 📁 Project Structure

```
src/
├── app/
│   ├── api/auth/          # Authentication API routes
│   ├── dashboard/         # Protected dashboard page
│   ├── login/            # Login page
│   ├── register/         # Registration page
│   ├── verify/           # Code verification page
│   └── layout.tsx        # Root layout with providers
├── components/
│   ├── auth/             # Authentication components
│   └── ui/               # Reusable UI components
├── lib/
│   ├── auth.ts           # Authentication utilities
│   ├── prisma.ts         # Database client
│   ├── utils.ts          # General utilities
│   └── validations.ts    # Zod schemas
└── types/
    └── auth.ts           # TypeScript interfaces
```

## 🔄 Authentication Flow

1. **User Registration/Login** on primary domain
2. **Code Generation** - User requests 6-digit code
3. **Code Storage** - Code saved to database with expiry
4. **Cross-Domain Navigation** - User goes to secondary domain
5. **Code Verification** - User enters code on secondary domain
6. **Access Granted** - Successful verification provides access

## 🧪 Testing

To test the cross-domain functionality:

1. Start the development server on port 3000
2. Open `http://localhost:3000` for primary domain
3. Register/login and generate a code
4. Open `http://localhost:3000/verify` (simulating secondary domain)
5. Enter the generated code to verify cross-domain authentication

## 🚀 Production Deployment

1. **Environment Variables**: Update `.env.local` with production values
2. **Database**: Ensure MongoDB is accessible from production environment
3. **Domains**: Configure actual domain URLs in environment variables
4. **Security**: Generate strong NEXTAUTH_SECRET for production
5. **CORS**: Update middleware with production domain URLs

## 📝 API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/generate-code` - Generate authentication code
- `POST /api/auth/verify-code` - Verify authentication code
- `/api/auth/[...nextauth]` - NextAuth.js authentication routes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support or questions, please open an issue in the repository.
