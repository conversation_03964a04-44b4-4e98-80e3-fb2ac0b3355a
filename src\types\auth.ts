export interface User {
  id: string
  name?: string | null
  email: string
  emailVerified?: Date | null
  image?: string | null
  createdAt: Date
  updatedAt: Date
}

export interface AuthCode {
  id: string
  code: string
  userId: string
  expiresAt: Date
  used: boolean
  createdAt: Date
}

export interface AuthResponse {
  success: boolean
  message: string
  data?: any
  error?: string
}

export interface CodeGenerationResponse extends AuthResponse {
  data?: {
    code: string
    expiresAt: Date
  }
}

export interface CodeVerificationResponse extends AuthResponse {
  data?: {
    user: User
    sessionToken?: string
  }
}
