import { RegisterForm } from '@/components/auth/RegisterForm'

export default function RegisterPage() {
  return (
    <div className="min-h-screen flex">
      {/* Left Panel - Register Form */}
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-20 xl:px-24 bg-white">
        <div className="mx-auto w-full max-w-sm lg:w-96">
          <RegisterForm />
        </div>
      </div>

      {/* Right Panel - Testimonial */}
      <div className="hidden lg:block relative flex-1">
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600">
          <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>

        {/* Content */}
        <div className="relative h-full flex flex-col justify-center px-12 xl:px-16">
          <div className="max-w-md">
            <blockquote className="text-white">
              <p className="text-xl lg:text-2xl font-medium leading-relaxed mb-8">
                "The authentication system is incredibly smooth and secure. Our users love the seamless cross-domain experience."
              </p>

              <footer className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-lg">JD</span>
                  </div>
                </div>
                <div>
                  <div className="text-white font-semibold">John Doe</div>
                  <div className="text-purple-100 text-sm">CTO, TechCorp</div>
                  <div className="text-purple-100 text-sm">Software Solutions</div>
                </div>
              </footer>

              <div className="flex items-center mt-6 space-x-1">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
            </blockquote>
          </div>
        </div>
      </div>
    </div>
  )
}
