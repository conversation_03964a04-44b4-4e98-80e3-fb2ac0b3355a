'use client'

import { useState } from 'react'
import { CodeVerification } from '@/components/auth/CodeVerification'
import { User } from '@/types/auth'

export default function VerifyPage() {
  const [authenticatedUser, setAuthenticatedUser] = useState<User | null>(null)

  const handleSuccess = (user: User) => {
    setAuthenticatedUser(user)
    // Here you could set up a session for the secondary domain
    // or redirect to a protected area
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Secondary Domain Access
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          app.domain.com - Code Verification
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <CodeVerification onSuccess={handleSuccess} />
        
        {!authenticatedUser && (
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Need to get a code?{' '}
              <a 
                href={process.env.NEXT_PUBLIC_PRIMARY_DOMAIN || 'http://localhost:3000'} 
                className="font-medium text-blue-600 hover:text-blue-500"
                target="_blank"
                rel="noopener noreferrer"
              >
                Go to primary domain
              </a>
            </p>
          </div>
        )}
      </div>

      {authenticatedUser && (
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-2xl">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Welcome to the Secondary Domain!
              </h3>
              <p className="text-gray-600 mb-4">
                You have successfully authenticated using the cross-domain authentication system.
              </p>
              <div className="bg-green-50 p-4 rounded-md">
                <p className="text-green-800 font-semibold">Authentication Successful</p>
                <p className="text-green-700">You now have access to this secondary domain's features.</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
