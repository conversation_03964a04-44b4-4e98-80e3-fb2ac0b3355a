import { getProviders } from 'next-auth/react'

export default async function TestOAuthPage() {
  const providers = await getProviders()

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">OAuth Providers Test</h1>
        
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-700">Available Providers:</h2>
          
          {providers ? (
            <div className="space-y-2">
              {Object.values(providers).map((provider) => (
                <div key={provider.id} className="p-3 border border-gray-200 rounded-lg">
                  <div className="font-medium text-gray-900">{provider.name}</div>
                  <div className="text-sm text-gray-600">ID: {provider.id}</div>
                  <div className="text-sm text-gray-600">Type: {provider.type}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-red-600">No providers found</div>
          )}
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h2 className="text-lg font-semibold text-gray-700 mb-3">Environment Check:</h2>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Google Client ID:</span>
              <span className={process.env.GOOGLE_CLIENT_ID ? 'text-green-600' : 'text-red-600'}>
                {process.env.GOOGLE_CLIENT_ID ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Google Client Secret:</span>
              <span className={process.env.GOOGLE_CLIENT_SECRET ? 'text-green-600' : 'text-red-600'}>
                {process.env.GOOGLE_CLIENT_SECRET ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>NextAuth URL:</span>
              <span className={process.env.NEXTAUTH_URL ? 'text-green-600' : 'text-red-600'}>
                {process.env.NEXTAUTH_URL ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <a 
            href="/login" 
            className="w-full bg-emerald-700 hover:bg-emerald-800 text-white font-medium py-3 px-4 rounded-lg transition-colors inline-block text-center"
          >
            Go to Login Page
          </a>
        </div>
      </div>
    </div>
  )
}
