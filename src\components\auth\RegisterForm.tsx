'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import axios from 'axios'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { registerSchema, type RegisterInput } from '@/lib/validations'
import { AuthResponse } from '@/types/auth'

export function RegisterForm() {
  const [formData, setFormData] = useState<RegisterInput>({
    name: '',
    email: '',
    password: '',
  })
  const [errors, setErrors] = useState<Partial<RegisterInput>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [generalError, setGeneralError] = useState('')
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})
    setGeneralError('')

    try {
      const validatedFields = registerSchema.parse(formData)
      
      const response = await axios.post<AuthResponse>('/api/auth/register', validatedFields)

      if (response.data.success) {
        setSuccess(true)
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setGeneralError(error.response.data.message)
      } else if (error.errors) {
        const fieldErrors: Partial<RegisterInput> = {}
        error.errors.forEach((err: any) => {
          if (err.path) {
            fieldErrors[err.path[0] as keyof RegisterInput] = err.message
          }
        })
        setErrors(fieldErrors)
      } else {
        setGeneralError('An unexpected error occurred')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (errors[name as keyof RegisterInput]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  if (success) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="text-green-600 text-lg font-semibold mb-2">
              Registration Successful!
            </div>
            <p className="text-gray-600">
              Redirecting to login page...
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Create Account</CardTitle>
        <CardDescription>Sign up for a new account</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Full Name"
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            error={errors.name}
            placeholder="Enter your full name"
            required
          />
          
          <Input
            label="Email"
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            error={errors.email}
            placeholder="Enter your email"
            required
          />
          
          <Input
            label="Password"
            type="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            error={errors.password}
            placeholder="Enter your password"
            required
          />

          {generalError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {generalError}
            </div>
          )}

          <Button type="submit" loading={isLoading} className="w-full">
            Create Account
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
