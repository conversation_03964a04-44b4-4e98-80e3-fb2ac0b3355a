import bcrypt from 'bcryptjs'
import { prisma } from './prisma'

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export function generateAuthCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

export async function createAuthCode(userId: string): Promise<string> {
  // Clean up expired codes for this user
  await prisma.authCode.deleteMany({
    where: {
      userId,
      OR: [
        { expiresAt: { lt: new Date() } },
        { used: true }
      ]
    }
  })

  const code = generateAuthCode()
  const expiresAt = new Date()
  expiresAt.setMinutes(expiresAt.getMinutes() + parseInt(process.env.AUTH_CODE_EXPIRY_MINUTES || '15'))

  await prisma.authCode.create({
    data: {
      code,
      userId,
      expiresAt,
    }
  })

  return code
}

export async function verifyAuthCode(code: string): Promise<{ valid: boolean; userId?: string }> {
  const authCode = await prisma.authCode.findUnique({
    where: { code },
    include: { user: true }
  })

  if (!authCode || authCode.used || authCode.expiresAt < new Date()) {
    return { valid: false }
  }

  // Mark code as used
  await prisma.authCode.update({
    where: { id: authCode.id },
    data: { used: true }
  })

  return { valid: true, userId: authCode.userId }
}
