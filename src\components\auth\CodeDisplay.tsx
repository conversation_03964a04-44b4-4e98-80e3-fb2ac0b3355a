'use client'

import { useState, useEffect } from 'react'
import axios from 'axios'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { CodeGenerationResponse } from '@/types/auth'
import { formatTime } from '@/lib/utils'

export function CodeDisplay() {
  const [code, setCode] = useState<string | null>(null)
  const [expiresAt, setExpiresAt] = useState<Date | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [timeLeft, setTimeLeft] = useState<string>('')

  useEffect(() => {
    if (expiresAt) {
      const timer = setInterval(() => {
        const now = new Date()
        const expiry = new Date(expiresAt)
        const diff = expiry.getTime() - now.getTime()

        if (diff <= 0) {
          setTimeLeft('Expired')
          setCode(null)
          setExpiresAt(null)
        } else {
          const minutes = Math.floor(diff / 60000)
          const seconds = Math.floor((diff % 60000) / 1000)
          setTimeLeft(`${minutes}:${seconds.toString().padStart(2, '0')}`)
        }
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [expiresAt])

  const generateCode = async () => {
    setIsLoading(true)
    setError('')

    try {
      const response = await axios.post<CodeGenerationResponse>('/api/auth/generate-code')
      
      if (response.data.success && response.data.data) {
        setCode(response.data.data.code)
        setExpiresAt(new Date(response.data.data.expiresAt))
      } else {
        setError(response.data.message || 'Failed to generate code')
      }
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to generate code')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async () => {
    if (code) {
      try {
        await navigator.clipboard.writeText(code)
        // You could add a toast notification here
      } catch (err) {
        console.error('Failed to copy code:', err)
      }
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Cross-Domain Authentication</CardTitle>
        <CardDescription>
          Generate a 6-digit code to authenticate on the secondary domain
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!code ? (
          <Button onClick={generateCode} loading={isLoading} className="w-full">
            Generate Authentication Code
          </Button>
        ) : (
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-mono font-bold tracking-wider bg-gray-100 p-4 rounded-lg">
                {code}
              </div>
              <p className="text-sm text-gray-600 mt-2">
                Expires in: <span className="font-semibold">{timeLeft}</span>
              </p>
            </div>
            
            <div className="flex space-x-2">
              <Button onClick={copyToClipboard} variant="outline" className="flex-1">
                Copy Code
              </Button>
              <Button onClick={generateCode} loading={isLoading} className="flex-1">
                Generate New
              </Button>
            </div>
            
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
              <p className="font-semibold mb-1">How to use:</p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Copy the 6-digit code above</li>
                <li>Navigate to the secondary domain (app.domain.com)</li>
                <li>Enter the code to authenticate</li>
              </ol>
            </div>
          </div>
        )}

        {error && (
          <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
            {error}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
