import { LoginForm } from '@/components/auth/LoginForm'

export default function LoginPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Geometric Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-800 via-green-700 to-emerald-900">
        {/* Geometric Shapes */}
        <div className="absolute inset-0">
          {/* Large triangle top-right */}
          <div className="absolute top-0 right-0 w-96 h-96 bg-green-600 opacity-30 transform rotate-45 translate-x-48 -translate-y-48"></div>

          {/* Medium triangle bottom-left */}
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-emerald-600 opacity-40 transform rotate-12 -translate-x-32 translate-y-32"></div>

          {/* Small triangle middle-left */}
          <div className="absolute top-1/3 left-0 w-32 h-32 bg-green-500 opacity-50 transform -rotate-12 -translate-x-16"></div>

          {/* Additional geometric elements */}
          <div className="absolute top-1/4 right-1/4 w-24 h-24 bg-emerald-500 opacity-30 transform rotate-45"></div>
          <div className="absolute bottom-1/3 right-0 w-40 h-40 bg-green-600 opacity-25 transform rotate-12 translate-x-20"></div>
        </div>
      </div>

      {/* Centered Modal */}
      <div className="relative z-10 min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-md">
          <LoginForm />
        </div>
      </div>
    </div>
  )
}
