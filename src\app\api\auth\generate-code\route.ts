import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth-config'
import { createAuthCode } from '@/lib/auth'
import { CodeGenerationResponse } from '@/types/auth'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.email) {
      return NextResponse.json<CodeGenerationResponse>(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get user ID from session
    const userId = session.user.id

    if (!userId) {
      return NextResponse.json<CodeGenerationResponse>(
        { success: false, message: 'User ID not found in session' },
        { status: 400 }
      )
    }

    const code = await createAuthCode(userId)
    const expiresAt = new Date()
    expiresAt.setMinutes(expiresAt.getMinutes() + parseInt(process.env.AUTH_CODE_EXPIRY_MINUTES || '15'))

    return NextResponse.json<CodeGenerationResponse>(
      {
        success: true,
        message: 'Authentication code generated successfully',
        data: { code, expiresAt }
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Code generation error:', error)
    return NextResponse.json<CodeGenerationResponse>(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
