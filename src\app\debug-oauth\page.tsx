'use client'

import { useEffect, useState } from 'react'
import { getProviders, signIn } from 'next-auth/react'
import type { LiteralUnion, ClientSafeProvider } from 'next-auth/react'
import type { BuiltInProviderType } from 'next-auth/providers'

export default function DebugOAuthPage() {
  const [providers, setProviders] = useState<Record<LiteralUnion<BuiltInProviderType, string>, ClientSafeProvider> | null>(null)
  const [debugInfo, setDebugInfo] = useState<any>(null)

  useEffect(() => {
    const loadProviders = async () => {
      const providers = await getProviders()
      setProviders(providers)
      
      // Get debug information
      const debugInfo = {
        currentUrl: window.location.origin,
        expectedRedirectUri: `${window.location.origin}/api/auth/callback/google`,
        nextAuthUrl: process.env.NEXTAUTH_URL || 'Not set',
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }
      setDebugInfo(debugInfo)
    }
    
    loadProviders()
  }, [])

  const testGoogleOAuth = () => {
    console.log('Testing Google OAuth with redirect URI:', `${window.location.origin}/api/auth/callback/google`)
    signIn('google', { 
      callbackUrl: '/dashboard',
      redirect: true 
    })
  }

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Google OAuth Debug Information</h1>
        
        {/* Debug Information */}
        <div className="mb-8 p-4 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">Configuration Details</h2>
          {debugInfo && (
            <div className="space-y-2 text-sm font-mono">
              <div><strong>Current URL:</strong> {debugInfo.currentUrl}</div>
              <div><strong>Expected Redirect URI:</strong> <span className="text-blue-600">{debugInfo.expectedRedirectUri}</span></div>
              <div><strong>NEXTAUTH_URL:</strong> {debugInfo.nextAuthUrl}</div>
              <div><strong>Timestamp:</strong> {debugInfo.timestamp}</div>
            </div>
          )}
        </div>

        {/* Copy-Paste Section */}
        <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h2 className="text-lg font-semibold text-blue-800 mb-3">📋 Copy This Exact URI to Google Cloud Console</h2>
          <div className="bg-white p-3 rounded border font-mono text-sm break-all">
            {debugInfo?.expectedRedirectUri || 'Loading...'}
          </div>
          <p className="text-sm text-blue-700 mt-2">
            Go to Google Cloud Console → APIs & Services → Credentials → Your OAuth Client → Authorized redirect URIs
          </p>
        </div>

        {/* Providers Information */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-700 mb-4">Available Providers</h2>
          {providers ? (
            <div className="space-y-3">
              {Object.values(providers).map((provider) => (
                <div key={provider.id} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="font-medium text-gray-900">{provider.name}</div>
                      <div className="text-sm text-gray-600">ID: {provider.id}</div>
                      <div className="text-sm text-gray-600">Type: {provider.type}</div>
                    </div>
                    {provider.id === 'google' && (
                      <button
                        onClick={testGoogleOAuth}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm"
                      >
                        Test Google OAuth
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-600">Loading providers...</div>
          )}
        </div>

        {/* Environment Check */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold text-gray-700 mb-3">Environment Variables Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-3 border border-gray-200 rounded">
              <div className="text-sm font-medium text-gray-700">Google Client ID</div>
              <div className="text-xs text-gray-500 mt-1">
                {process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ? '✅ Set (Public)' : '❌ Not set as public'}
              </div>
            </div>
            <div className="p-3 border border-gray-200 rounded">
              <div className="text-sm font-medium text-gray-700">NextAuth URL</div>
              <div className="text-xs text-gray-500 mt-1">
                {debugInfo?.nextAuthUrl !== 'Not set' ? '✅ Set' : '❌ Not set'}
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">🔧 Fix Steps</h3>
          <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-700">
            <li>Copy the redirect URI from the blue box above</li>
            <li>Go to Google Cloud Console</li>
            <li>Navigate to APIs & Services → Credentials</li>
            <li>Edit your OAuth 2.0 Client ID</li>
            <li>Add the copied URI to "Authorized redirect URIs"</li>
            <li>Save the changes</li>
            <li>Test the "Test Google OAuth" button above</li>
          </ol>
        </div>

        <div className="mt-6 flex space-x-4">
          <a 
            href="/login" 
            className="flex-1 bg-emerald-700 hover:bg-emerald-800 text-white font-medium py-3 px-4 rounded-lg transition-colors text-center"
          >
            Go to Login Page
          </a>
          <button
            onClick={() => window.location.reload()}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            Refresh Debug Info
          </button>
        </div>
      </div>
    </div>
  )
}
