import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

export default withAuth(
  function middleware(req) {
    // Add CORS headers for cross-domain requests
    const response = NextResponse.next()

    // Allow requests from both domains
    const allowedOrigins = [
      process.env.PRIMARY_DOMAIN || 'http://localhost:3000',
      process.env.SECONDARY_DOMAIN || 'http://localhost:3001',
      'http://localhost:3000',
      'http://localhost:3001'
    ]

    const origin = req.headers.get('origin')
    if (origin && allowedOrigins.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin)
    }

    response.headers.set('Access-Control-Allow-Credentials', 'true')
    response.headers.set('Access-Control-Allow-Methods', 'GET,DELETE,PATCH,POST,PUT')
    response.headers.set(
      'Access-Control-Allow-Headers',
      'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version'
    )

    return response
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to auth routes and verify page without authentication
        if (
          req.nextUrl.pathname.startsWith('/api/auth') ||
          req.nextUrl.pathname === '/login' ||
          req.nextUrl.pathname === '/register' ||
          req.nextUrl.pathname === '/verify' ||
          req.nextUrl.pathname === '/'
        ) {
          return true
        }

        // Require authentication for dashboard and other protected routes
        return !!token
      },
    },
    pages: {
      signIn: '/login',
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
