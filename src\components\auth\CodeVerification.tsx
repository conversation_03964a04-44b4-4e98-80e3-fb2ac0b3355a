'use client'

import { useState } from 'react'
import axios from 'axios'
import { But<PERSON> } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { codeVerificationSchema, type CodeVerificationInput } from '@/lib/validations'
import { CodeVerificationResponse } from '@/types/auth'

interface CodeVerificationProps {
  onSuccess?: (user: any) => void
}

export function CodeVerification({ onSuccess }: CodeVerificationProps) {
  const [formData, setFormData] = useState<CodeVerificationInput>({
    code: '',
  })
  const [errors, setErrors] = useState<Partial<CodeVerificationInput>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [generalError, setGeneralError] = useState('')
  const [success, setSuccess] = useState(false)
  const [user, setUser] = useState<any>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setErrors({})
    setGeneralError('')

    try {
      const validatedFields = codeVerificationSchema.parse(formData)
      
      const response = await axios.post<CodeVerificationResponse>('/api/auth/verify-code', validatedFields)

      if (response.data.success && response.data.data) {
        setSuccess(true)
        setUser(response.data.data.user)
        onSuccess?.(response.data.data.user)
      } else {
        setGeneralError(response.data.message || 'Verification failed')
      }
    } catch (error: any) {
      if (error.response?.data?.message) {
        setGeneralError(error.response.data.message)
      } else if (error.errors) {
        const fieldErrors: Partial<CodeVerificationInput> = {}
        error.errors.forEach((err: any) => {
          if (err.path) {
            fieldErrors[err.path[0] as keyof CodeVerificationInput] = err.message
          }
        })
        setErrors(fieldErrors)
      } else {
        setGeneralError('An unexpected error occurred')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    // Only allow numbers and limit to 6 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 6)
    setFormData(prev => ({ ...prev, [name]: numericValue }))
    if (errors[name as keyof CodeVerificationInput]) {
      setErrors(prev => ({ ...prev, [name]: undefined }))
    }
  }

  if (success && user) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="text-green-600">Authentication Successful!</CardTitle>
          <CardDescription>Welcome to the secondary domain</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="font-semibold text-green-800">Welcome, {user.name || user.email}!</p>
              <p className="text-sm text-green-600">You have been successfully authenticated.</p>
            </div>
            
            <div className="text-sm text-gray-600">
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Account Created:</strong> {new Date(user.createdAt).toLocaleDateString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Enter Authentication Code</CardTitle>
        <CardDescription>
          Enter the 6-digit code from the primary domain to authenticate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="6-Digit Code"
            type="text"
            name="code"
            value={formData.code}
            onChange={handleChange}
            error={errors.code}
            placeholder="123456"
            maxLength={6}
            className="text-center text-2xl font-mono tracking-wider"
            required
          />

          {generalError && (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
              {generalError}
            </div>
          )}

          <Button 
            type="submit" 
            loading={isLoading} 
            className="w-full"
            disabled={formData.code.length !== 6}
          >
            Verify Code
          </Button>
        </form>

        <div className="mt-4 text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
          <p className="font-semibold mb-1">Need a code?</p>
          <p>Go to the primary domain, log in, and generate an authentication code.</p>
        </div>
      </CardContent>
    </Card>
  )
}
