# Google OAuth Setup Guide for QueueForm

## Overview
This guide explains how to set up Google OAuth authentication for the QueueForm login system. The implementation allows users to sign in using their Google accounts in addition to the existing email/password authentication.

## Prerequisites
- Google Cloud Console account
- QueueForm application running locally or deployed
- Access to environment variables configuration

## Step 1: Create Google Cloud Project

1. **Go to Google Cloud Console**
   - Visit [Google Cloud Console](https://console.cloud.google.com/)
   - Sign in with your Google account

2. **Create a New Project**
   - Click "Select a project" dropdown
   - Click "New Project"
   - Enter project name: `QueueForm Auth`
   - Click "Create"

## Step 2: Enable Google+ API

1. **Navigate to APIs & Services**
   - In the left sidebar, click "APIs & Services" > "Library"
   - Search for "Google+ API"
   - Click on "Google+ API" and click "Enable"

2. **Enable People API (Alternative)**
   - If Google+ API is deprecated, enable "People API" instead
   - This provides access to user profile information

## Step 3: Configure OAuth Consent Screen

1. **Go to OAuth Consent Screen**
   - Navigate to "APIs & Services" > "OAuth consent screen"
   - Choose "External" user type (for testing with any Google account)
   - Click "Create"

2. **Fill Required Information**
   ```
   App name: QueueForm
   User support email: <EMAIL>
   Developer contact information: <EMAIL>
   ```

3. **Add Scopes**
   - Click "Add or Remove Scopes"
   - Add these scopes:
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
   - Click "Update"

4. **Add Test Users (for development)**
   - Add your email and any test user emails
   - Click "Save and Continue"

## Step 4: Create OAuth 2.0 Credentials

1. **Go to Credentials**
   - Navigate to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"

2. **Configure OAuth Client**
   ```
   Application type: Web application
   Name: QueueForm Web Client
   
   Authorized JavaScript origins:
   - http://localhost:3000 (for development)
   - https://yourdomain.com (for production)
   
   Authorized redirect URIs:
   - http://localhost:3000/api/auth/callback/google (for development)
   - https://yourdomain.com/api/auth/callback/google (for production)
   ```

3. **Save Credentials**
   - Click "Create"
   - Copy the Client ID and Client Secret
   - Store them securely

## Step 5: Update Environment Variables

1. **Update .env.local file**
   ```env
   # Google OAuth
   GOOGLE_CLIENT_ID="your-actual-google-client-id"
   GOOGLE_CLIENT_SECRET="your-actual-google-client-secret"
   ```

2. **For Production Deployment**
   - Add the same environment variables to your hosting platform
   - Update the authorized origins and redirect URIs in Google Console

## Step 6: Test the Implementation

1. **Start the Development Server**
   ```bash
   npm run dev
   ```

2. **Test Google Login**
   - Navigate to `http://localhost:3000/login`
   - Click "Continue with Google"
   - Complete the Google OAuth flow
   - Verify successful login and redirect to dashboard

## Implementation Details

### NextAuth Configuration
The Google OAuth provider is configured in `src/lib/auth-config.ts`:

```typescript
GoogleProvider({
  clientId: process.env.GOOGLE_CLIENT_ID!,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
})
```

### Database Integration
- Uses Prisma adapter for database sessions
- Automatically creates user accounts for Google OAuth users
- Stores OAuth account information in the `accounts` table
- Manages sessions in the `sessions` table

### User Experience Flow
1. User clicks "Continue with Google"
2. Redirected to Google OAuth consent screen
3. User grants permissions
4. Redirected back to application
5. User account created/updated automatically
6. User logged in and redirected to dashboard

## Security Considerations

1. **Environment Variables**
   - Never commit actual credentials to version control
   - Use different credentials for development and production
   - Rotate credentials regularly

2. **Redirect URIs**
   - Only add trusted domains to authorized redirect URIs
   - Use HTTPS in production
   - Validate redirect URIs match exactly

3. **Scopes**
   - Only request necessary permissions
   - Current implementation requests email and profile only

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" Error**
   - Verify redirect URI in Google Console matches exactly
   - Check for trailing slashes or protocol mismatches

2. **"invalid_client" Error**
   - Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are correct
   - Check environment variables are loaded properly

3. **"access_denied" Error**
   - User denied permissions
   - Check OAuth consent screen configuration

4. **Database Connection Issues**
   - Ensure Prisma schema is up to date
   - Run `npx prisma db push` to sync schema

### Debug Mode
Enable NextAuth debug mode by setting:
```env
NEXTAUTH_DEBUG=true
```

## Production Deployment

1. **Update Google Console**
   - Add production domain to authorized origins
   - Add production callback URL to redirect URIs

2. **Environment Variables**
   - Set all required environment variables in production
   - Use secure credential management

3. **HTTPS Required**
   - Google OAuth requires HTTPS in production
   - Ensure SSL certificate is properly configured

## Support

For additional help:
- [NextAuth.js Google Provider Documentation](https://next-auth.js.org/providers/google)
- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Prisma NextAuth Adapter Documentation](https://authjs.dev/reference/adapter/prisma)
