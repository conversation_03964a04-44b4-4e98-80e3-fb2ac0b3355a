import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { verifyAuthCode } from '@/lib/auth'
import { codeVerificationSchema } from '@/lib/validations'
import { CodeVerificationResponse } from '@/types/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedFields = codeVerificationSchema.parse(body)

    const verification = await verifyAuthCode(validatedFields.code)

    if (!verification.valid || !verification.userId) {
      return NextResponse.json<CodeVerificationResponse>(
        { success: false, message: 'Invalid or expired authentication code' },
        { status: 400 }
      )
    }

    // Get user details
    const user = await prisma.user.findUnique({
      where: { id: verification.userId },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        image: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!user) {
      return NextResponse.json<CodeVerificationResponse>(
        { success: false, message: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json<CodeVerificationResponse>(
      {
        success: true,
        message: 'Authentication code verified successfully',
        data: { user }
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Code verification error:', error)
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json<CodeVerificationResponse>(
        { success: false, message: 'Invalid code format', error: error.message },
        { status: 400 }
      )
    }

    return NextResponse.json<CodeVerificationResponse>(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
